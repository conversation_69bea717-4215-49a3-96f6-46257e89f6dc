package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.exception.JWIRepoException;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.neo4j.execution.CommonEntityTemplate;
import cn.jwis.framework.database.neo4j.util.ConditionParserNeo4j;
import cn.jwis.framework.database.neo4j.util.JSONUtil;
import cn.jwis.platform.plm.foundation.relationship.Use;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.response.IBOMNode;
import com.alibaba.fastjson.JSONObject;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.neo4j.driver.Value;
import org.neo4j.driver.types.Node;
import org.neo4j.driver.types.Relationship;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class CustomerPartIterationRepoImpl extends CommonEntityTemplate<PartIteration> {


    /**
     * 新的查询bom的方法，支持将bom中的规格查询出来
     * @param parentOid
     * @param rootOid
     * @param maxLevel
     * @param structNodeClass
     * @param split
     * @return
     * @param <T>
     */
    public <T extends IBOMNode> List<T> findUseTree(String parentOid, String rootOid, Integer maxLevel,
                                                    Class<T> structNodeClass, String[] split) {

        // 参数准备
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rootOid", rootOid);
        paramMap.put("parentOid", parentOid);
        paramMap.put("globalType", "GLOBAL_SUBSTITUTE");
        paramMap.put("tenantOid", SessionHelper.getCurrentUser().getTenantOid());

        // 构建 Cypher 查询语句
        StringBuilder cypher = new StringBuilder();
        cypher.append("MATCH (root:PartIteration)-[r:USE*0..")
                .append(maxLevel != null ? maxLevel : "")
                .append("]->(s:PartIteration) ")
                .append("WHERE root.oid = $rootOid AND root.tenantOid = $tenantOid ")
                .append("AND all(row IN r WHERE row.entityLatest ")
                .append("AND (startNode(row).latest = false ")
                .append("OR endNode(row).lockOwnerOid IS NULL ")
                .append("OR (endNode(row).lockOwnerOid = $userOid AND endNode(row).lockSourceOid IS NOT NULL) ")
                .append("OR (endNode(row).lockOwnerOid <> $userOid AND endNode(row).lockSourceOid IS NULL))) ")
                .append("WITH DISTINCT r[-1] AS interRel, s ")
                .append("WITH interRel, s, startNode(interRel).oid AS preSOid ")

                // 查询 PartIteration 的 PropertyGroup 关系，获取 cn_jwis_gg 属性
                .append("OPTIONAL MATCH (s)-[:ATTRIBUTE]->(pro:PropertyGroup) ")
                .append("WITH interRel, s, preSOid, pro ")

                // 统计全局替代 global
                .append("OPTIONAL MATCH (s)<-[:ITERATE]-(:Part)-[r:GLOBAL_SUBSTITUTE]->()")
                .append("-[:ITERATE]->(global) USING JOIN ON s ")
                .append("WHERE global.latest = true AND ")
                .append(lockFilterCypher("global", paramMap)).append(" ")
                .append("WITH interRel, s, preSOid, pro, size(collect(global)) AS globalNodes ")

                // 统计局部替代 local
                .append("OPTIONAL MATCH (s)-[locR:LOCAL_SUBSTITUTE]->(local) ")
                .append("WHERE locR.parentPartOid = preSOid ")
                .append("RETURN interRel, s, pro, globalNodes, size(collect(local)) AS localNodes");

        // 执行查询
        Result result = run(cypher.toString(), paramMap);

        // 结果转换为 BOM 节点结构
        List<T> nodes = dbResult2BOMV2(result, "interRel", "s", "pro", "globalNodes", "localNodes", structNodeClass, split);

        // 查询结果为空的情况：返回 root 本身
        if (CollectionUtil.isEmpty(nodes)) {
            PartIteration rootNode = (PartIteration) findByOid(rootOid);
            if (rootNode == null) {
                return new ArrayList<>();
            }

            try {
                T rootStructNode = structNodeClass.newInstance();
                BeanUtil.copyProperties(rootNode, rootStructNode);

                JSONObject json = (JSONObject) JSONObject.toJSON(rootNode);
                String identity = analysisDisplayName(split, json);
                rootStructNode.setIdentity(identity);

                return Collections.singletonList(rootStructNode);
            } catch (Exception e) {
                throw new JWIRepoException(e);
            }
        }

        return nodes;
    }

    private <T extends IBOMNode> List<T> dbResult2BOMV2(
            Result result,
            String relVar,
            String nodeVar,
            String propertyGroupVar,
            String globalVar,
            String localVar,
            Class<T> structNodeClass,
            String[] split) {

        Map<String, T> relOidToNodeMap = new HashMap<>();
        Map<String, List<T>> nodeOidToNodeListMap = new HashMap<>();
        Map<String, List<String>> fromOidToRelOidListMap = new HashMap<>();

        while (result.hasNext()) {
            Record record = result.next();
            Value relValue = record.get(relVar);
            Node nodeValue = record.get(nodeVar).asNode();
            Value propertyGroupValue = record.get(propertyGroupVar);

            // 构建 IBOMNode 实例
            T node = JSONUtil.parseObject(nodeValue, structNodeClass);
            JSONObject json = (JSONObject) JSONObject.toJSON(node);
            String identity = analysisDisplayName(split, json);
            node.setIdentity(identity);

            // 设置替代关系标识
            int globalCount = record.get(globalVar).asInt();
            int localCount = record.get(localVar).asInt();
            node.setSubstitutedFlag(globalCount > 0 || localCount > 0);

            // 处理结构关系
            String relOid = "";
            String fromOid = null;
            if (!relValue.isNull()) {
                Relationship rel = relValue.asRelationship();
                Use use = JSONUtil.parseObject(rel, Use.class);

                // 处理 PropertyGroup 中的 cn_jwis_gg 属性
                if (!propertyGroupValue.isNull()) {
                    Node propertyGroupNode = propertyGroupValue.asNode();

                    // 直接从 Node 的 asMap() 获取属性，避免类型不匹配
                    Map<String, Object> propertyGroupMap = propertyGroupNode.asMap();

                    // 获取 cn_jwis_gg 属性值
                    Object cnJwisGgObj = propertyGroupMap.get("cn_jwis_gg");
                    if (cnJwisGgObj != null) {
                        String cnJwisGg = cnJwisGgObj.toString();

                        // 获取或创建 Use 的 extensionContent
                        JSONObject extensionContent = use.getExtensionContent();
                        if (extensionContent == null) {
                            extensionContent = new JSONObject();
                        }

                        // 将 cn_jwis_gg 属性值放入 Use 的 extensionContent 中
                        extensionContent.put("cn_jwis_gg", cnJwisGg);
                        use.setExtensionContent(extensionContent);
                    }
                }

                relOid = use.getOid();
                fromOid = use.getFromOid();
                node.setUse(use);

                // 维护关系映射
                relOidToNodeMap.put(relOid, node);
                if (fromOid != null) {
                    MapUtil.putIntoMapList(fromOidToRelOidListMap, fromOid, relOid);
                }
            } else {
                // 根节点
                relOidToNodeMap.put("", node);
            }

            // 按 PartIteration 的 OID 存储节点（多节点同一 OID 的情况）
            MapUtil.putIntoMapList(nodeOidToNodeListMap, node.getOid(), node);
        }

        // 建立父子层级关系
        for (Map.Entry<String, List<String>> entry : fromOidToRelOidListMap.entrySet()) {
            String fromOid = entry.getKey();
            List<String> relOidList = entry.getValue();
            List<T> parentNodes = nodeOidToNodeListMap.get(fromOid);
            if (CollectionUtil.isEmpty(parentNodes)) continue;

            List<T> children = relOidList.stream()
                    .map(relOidToNodeMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            for (T parent : parentNodes) {
                List<T> existingChildren = parent.getChildren();
                if (existingChildren == null) {
                    parent.setChildren(children);
                } else {
                    existingChildren.addAll(children);
                }
            }
        }

        // 返回最终结果（根节点为 relOid == ""）
        T rootNode = relOidToNodeMap.get("");
        if (rootNode == null) {
            return null;
        }
        return Collections.singletonList(rootNode);
    }

    private String lockFilterCypher(String nodeVar, Map param) {
        param.put("userOid", SessionHelper.getCurrentUser().getOid());
        String template = "( {0}.lockOwnerOid is null or ({0}.lockOwnerOid=$userOid and {0}.lockSourceOid is not null) or ({0}.lockOwnerOid<>$userOid and {0}.lockSourceOid is null ))";
        return MessageFormat.format(template, nodeVar);
    }
    private String analysisDisplayName(String[] split, JSONObject json) {
        if (ObjectUtils.isEmpty(split)) {
            return json.getString("name");
        } else {
            String result = "";
            String format = split[0];
            String value = split[1];
            List<String> list = Arrays.asList(value.split("[,]"));
            String[] array = new String[list.size()];

            for(int i = 0; i < list.size(); ++i) {
                String key = (String)list.get(i);
                String s = (String)Optional.ofNullable(json.getString(key)).orElse("");
                array[i] = s;
            }

            try {
                result = MessageFormat.format(format, array);
            } catch (Exception var11) {
                result = json.getString("name");
            }

            return result;
        }
    }

    /**
     * BOM查询接口
     * @param parentOid
     * @param rootOid
     * @param maxLevel
     * @param condition
     * @param structNodeClass
     * @return
     * @param <T>
     */
    public <T extends IBOMNode> List<T> fuzzyUseTree(String parentOid, String rootOid, Integer maxLevel,
                                                     Condition condition, Class<T> structNodeClass) {
        Map<String, Object> params = new HashMap<>();
        params.put("rootOid", rootOid);
        params.put("parentOid", parentOid);
        params.put("tenantOid", SessionHelper.getCurrentUser().getTenantOid());

        StringBuilder cypher = new StringBuilder();

        cypher.append("MATCH path=(root:PartIteration)-[r:USE*0..")
                .append(maxLevel == null ? "" : maxLevel)
                .append("]->(s:PartIteration) ")
                .append("WHERE root.oid = $rootOid ")
                .append("AND root.tenantOid = $tenantOid ")
                .append("AND all(row IN r WHERE row.entityLatest ")
                .append("AND ( startNode(row).latest = false ")
                .append("OR endNode(row).lockOwnerOid IS NULL ")
                .append("OR (endNode(row).lockOwnerOid = $userOid AND endNode(row).lockSourceOid IS NOT NULL) ")
                .append("OR (endNode(row).lockOwnerOid <> $userOid AND endNode(row).lockSourceOid IS NULL))) ");

        if (condition == null) {
            cypher.append("WITH DISTINCT r[-1] AS interRel, s ");
        } else {
            String parsedCondition = condition.parse(new ConditionParserNeo4j("k", params));
            cypher.append("AND any(k IN nodes(path) WHERE ").append(parsedCondition).append(") ");
            cypher.append("WITH nodes(path) AS ns, r ")
                    .append("UNWIND [i IN range(0, size(r)) | [ns[i], CASE i WHEN 0 THEN null ELSE r[i-1] END]] AS row ")
                    .append("WITH DISTINCT row[1] AS interRel, row[0] AS s ");
        }

        cypher.append("WITH interRel, s, startNode(interRel).oid AS preSOid ")
                .append("OPTIONAL MATCH (s)<-[:ITERATE]-(:Part)-[r:GLOBAL_SUBSTITUTE]->() ")
                .append("-[:ITERATE]->(global) USING JOIN ON s ")
                .append("WHERE global.latest = true AND ").append(lockFilterCypher("global", params)).append(" ")
                .append("WITH interRel, s, preSOid, size(collect(global)) AS globalNodes ")
                .append("OPTIONAL MATCH (s)-[locR:LOCAL_SUBSTITUTE]->(local) ")
                .append("WHERE locR.parentPartOid = preSOid AND ").append(lockFilterCypher("local", params)).append(" ")
                .append("RETURN interRel, s, globalNodes, size(collect(local)) AS localNodes");

        Result result = run(cypher.toString(), params);

        List<T> nodes = dbResult2BOM(result, "interRel", "s", "globalNodes", "localNodes", structNodeClass);

        if (CollectionUtil.isEmpty(nodes)) {
            PartIteration rootNode = (PartIteration) findByOid(rootOid);
            if (rootNode == null) {
                return new ArrayList<>();
            }

            try {
                T t = structNodeClass.newInstance();  // 推荐改为 getDeclaredConstructor().newInstance()
                BeanUtil.copyProperties(rootNode, t);
                return Collections.singletonList(t);
            } catch (Exception e) {
                throw new JWIRepoException(e);
            }
        }

        return nodes;
    }

    @SuppressWarnings("unchecked")
    private <T extends IBOMNode> List<T> dbResult2BOM(Result run, String relVar, String nodeVar, String globalVar, String localVar, Class<T> structNodeClass) {
        // Map<关系OID, 节点对象>
        Map<String, T> relOidToNode = new HashMap<>();
        // Map<节点OID, 所有对应的节点对象（去重或聚合）>
        Map<String, List<T>> nodeOidToNodes = new HashMap<>();
        // Map<父Use关系fromOid, 多个子Use关系OID>
        Map<String, List<String>> fromOidToRelOids = new HashMap<>();

        String blankOid = ""; // 顶级关系的默认key
        Class<Use> useClass = Use.class;

        while (run.hasNext()) {
            Record record = run.next();

            // 解析结构节点
            Node nodeNeo = record.get(nodeVar).asNode();
            T bomNode = JSONUtil.parseObject(nodeNeo, structNodeClass);

            // 默认无Use信息
            String fromOid = null;
            Use use = null;

            // 解析Use关系
            Value relValue = record.get(relVar);
            if (!relValue.isNull()) {
                Relationship relNeo = relValue.asRelationship();
                use = JSONUtil.parseObject(relNeo, useClass);
                fromOid = use.getFromOid();
                bomNode.setUse(use);
            }

            // 设置替代标志（全局或局部替代）
            boolean hasSubstitute = record.get(globalVar).asInt() > 0 || record.get(localVar).asInt() > 0;
            bomNode.setSubstitutedFlag(hasSubstitute);

            // 保存节点引用
            String relOid = (use == null) ? blankOid : use.getOid();
            relOidToNode.put(relOid, bomNode);

            // 构建父子关系映射
            if (fromOid != null) {
                MapUtil.putIntoMapList(fromOidToRelOids, fromOid, relOid);
            }

            // 建立当前节点 OID 到对象列表的映射（一个 OID 可能多次出现）
            MapUtil.putIntoMapList(nodeOidToNodes, bomNode.getOid(), bomNode);
        }

        // 建立父子结构树
        for (Map.Entry<String, List<String>> entry : fromOidToRelOids.entrySet()) {
            String fromOid = entry.getKey();
            List<String> relOids = entry.getValue();
            List<T> parentNodes = nodeOidToNodes.get(fromOid);

            if (CollectionUtil.isEmpty(parentNodes)) continue;

            // 通过 relOid 找出所有子节点
            List<T> children = new ArrayList<>();
            for (String childRelOid : relOids) {
                T child = relOidToNode.get(childRelOid);
                if (child != null) {
                    children.add(child);
                }
            }

            // 将这些子节点添加到所有匹配的父节点中
            for (T parent : parentNodes) {
                List<T> existingChildren = parent.getChildren();
                if (existingChildren == null) {
                    parent.setChildren(new ArrayList<>(children));
                } else {
                    existingChildren.addAll(children);
                }
            }
        }

        // 返回顶层节点（relOid 为空字符串）
        T rootNode = relOidToNode.get(blankOid);
        if (rootNode == null) {
            return null;
        }
        return Collections.singletonList(rootNode);
    }


}