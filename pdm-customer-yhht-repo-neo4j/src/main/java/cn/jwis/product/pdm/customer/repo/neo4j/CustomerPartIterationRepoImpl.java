package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.exception.JWIRepoException;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.neo4j.util.JSONUtil;
import cn.jwis.platform.plm.foundation.relationship.Use;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.response.IBOMNode;
import cn.jwis.product.pdm.partbom.repo.PartIterationRepoImpl;
import com.alibaba.fastjson.JSONObject;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.neo4j.driver.Value;
import org.neo4j.driver.types.Node;
import org.neo4j.driver.types.Relationship;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Primary
public class CustomerPartIterationRepoImpl extends PartIterationRepoImpl {

    @Override
    public <T extends IBOMNode> List<T> findUseTree(String parentOid, String rootOid, Integer maxLevel,
                                                    Class<T> structNodeClass, String[] split) {

        // 参数准备
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rootOid", rootOid);
        paramMap.put("parentOid", parentOid);
        paramMap.put("globalType", "GLOBAL_SUBSTITUTE");
        paramMap.put("tenantOid", SessionHelper.getCurrentUser().getTenantOid());

        // 构建 Cypher 查询语句
        StringBuilder cypher = new StringBuilder();
        cypher.append("MATCH (root:PartIteration)-[r:USE*0..")
                .append(maxLevel != null ? maxLevel : "")
                .append("]->(s:PartIteration) ")
                .append("WHERE root.oid = $rootOid AND root.tenantOid = $tenantOid ")
                .append("AND all(row IN r WHERE row.entityLatest ")
                .append("AND (startNode(row).latest = false ")
                .append("OR endNode(row).lockOwnerOid IS NULL ")
                .append("OR (endNode(row).lockOwnerOid = $userOid AND endNode(row).lockSourceOid IS NOT NULL) ")
                .append("OR (endNode(row).lockOwnerOid <> $userOid AND endNode(row).lockSourceOid IS NULL))) ")
                .append("WITH DISTINCT r[-1] AS interRel, s ")
                .append("WITH interRel, s, startNode(interRel).oid AS preSOid ")

                // 统计全局替代 global
                .append("OPTIONAL MATCH (s)<-[:ITERATE]-(:Part)-[r:GLOBAL_SUBSTITUTE]->()")
                .append("-[:ITERATE]->(global) USING JOIN ON s ")
                .append("WHERE global.latest = true AND ")
                .append(lockFilterCypher("global", paramMap)).append(" ")
                .append("WITH interRel, s, preSOid, size(collect(global)) AS globalNodes ")

                // 统计局部替代 local
                .append("OPTIONAL MATCH (s)-[locR:LOCAL_SUBSTITUTE]->(local) ")
                .append("WHERE locR.parentPartOid = preSOid ")
                .append("RETURN interRel, s, globalNodes, size(collect(local)) AS localNodes");

        // 执行查询
        Result result = run(cypher.toString(), paramMap);

        // 结果转换为 BOM 节点结构
        List<T> nodes = dbResult2BOMV2(result, "interRel", "s", "globalNodes", "localNodes", structNodeClass, split);

        // 查询结果为空的情况：返回 root 本身
        if (CollectionUtil.isEmpty(nodes)) {
            PartIteration rootNode = (PartIteration) findByOid(rootOid);
            if (rootNode == null) {
                return new ArrayList<>();
            }

            try {
                T rootStructNode = structNodeClass.newInstance();
                BeanUtil.copyProperties(rootNode, rootStructNode);

                JSONObject json = (JSONObject) JSONObject.toJSON(rootNode);
                String identity = analysisDisplayName(split, json);
                rootStructNode.setIdentity(identity);

                return Collections.singletonList(rootStructNode);
            } catch (Exception e) {
                throw new JWIRepoException(e);
            }
        }

        return nodes;
    }

    private <T extends IBOMNode> List<T> dbResult2BOMV2(
            Result result,
            String relVar,
            String nodeVar,
            String globalVar,
            String localVar,
            Class<T> structNodeClass,
            String[] split) {

        Map<String, T> relOidToNodeMap = new HashMap<>();
        Map<String, List<T>> nodeOidToNodeListMap = new HashMap<>();
        Map<String, List<String>> fromOidToRelOidListMap = new HashMap<>();

        while (result.hasNext()) {
            Record record = result.next();
            Value relValue = record.get(relVar);
            Node nodeValue = record.get(nodeVar).asNode();

            // 构建 IBOMNode 实例
            T node = JSONUtil.parseObject(nodeValue, structNodeClass);
            JSONObject json = (JSONObject) JSONObject.toJSON(node);
            String identity = analysisDisplayName(split, json);
            node.setIdentity(identity);

            // 设置替代关系标识
            int globalCount = record.get(globalVar).asInt();
            int localCount = record.get(localVar).asInt();
            node.setSubstitutedFlag(globalCount > 0 || localCount > 0);

            // 处理结构关系
            String relOid = "";
            String fromOid = null;
            if (!relValue.isNull()) {
                Relationship rel = relValue.asRelationship();
                Use use = JSONUtil.parseObject(rel, Use.class);
                relOid = use.getOid();
                fromOid = use.getFromOid();
                node.setUse(use);

                // 维护关系映射
                relOidToNodeMap.put(relOid, node);
                if (fromOid != null) {
                    MapUtil.putIntoMapList(fromOidToRelOidListMap, fromOid, relOid);
                }
            } else {
                // 根节点
                relOidToNodeMap.put("", node);
            }

            // 按 PartIteration 的 OID 存储节点（多节点同一 OID 的情况）
            MapUtil.putIntoMapList(nodeOidToNodeListMap, node.getOid(), node);
        }

        // 建立父子层级关系
        for (Map.Entry<String, List<String>> entry : fromOidToRelOidListMap.entrySet()) {
            String fromOid = entry.getKey();
            List<String> relOidList = entry.getValue();
            List<T> parentNodes = nodeOidToNodeListMap.get(fromOid);
            if (CollectionUtil.isEmpty(parentNodes)) continue;

            List<T> children = relOidList.stream()
                    .map(relOidToNodeMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            for (T parent : parentNodes) {
                List<T> existingChildren = parent.getChildren();
                if (existingChildren == null) {
                    parent.setChildren(children);
                } else {
                    existingChildren.addAll(children);
                }
            }
        }

        // 返回最终结果（根节点为 relOid == ""）
        T rootNode = relOidToNodeMap.get("");
        if (rootNode == null) {
            return null;
        }
        return Collections.singletonList(rootNode);
    }

    private String lockFilterCypher(String nodeVar, Map param) {
        param.put("userOid", SessionHelper.getCurrentUser().getOid());
        String template = "( {0}.lockOwnerOid is null or ({0}.lockOwnerOid=$userOid and {0}.lockSourceOid is not null) or ({0}.lockOwnerOid<>$userOid and {0}.lockSourceOid is null ))";
        return MessageFormat.format(template, nodeVar);
    }
    private String analysisDisplayName(String[] split, JSONObject json) {
        if (ObjectUtils.isEmpty(split)) {
            return json.getString("name");
        } else {
            String result = "";
            String format = split[0];
            String value = split[1];
            List<String> list = Arrays.asList(value.split("[,]"));
            String[] array = new String[list.size()];

            for(int i = 0; i < list.size(); ++i) {
                String key = (String)list.get(i);
                String s = (String)Optional.ofNullable(json.getString(key)).orElse("");
                array[i] = s;
            }

            try {
                result = MessageFormat.format(format, array);
            } catch (Exception var11) {
                result = json.getString("name");
            }

            return result;
        }
    }

}